import { create } from "zustand";

import { BaseEdge, BuiltinNode, WorkflowData } from "@/types/workflow";
import { PanelState, WorkflowStore } from "@/types/zustand/workflow";
import { v4 as uuid } from "uuid";

export const useWorkflowStore = create<WorkflowStore>((set, get) => ({
  currentWorkflow: null,
  selectedNodeId: null,
  isDebugMode: false,
  isDirty: false,
  debugPanel: {
    isOpen: false,
    width: 360,
  },
  configPanel: {
    isOpen: false,
    width: 360,
  },

  setCurrentWorkflow: (workflow: WorkflowData) => {
    set({ currentWorkflow: workflow });
  },
  setSelectedNodeId: (id: string | null) => {
    set({ selectedNodeId: id });
    // 当选择节点时自动打开配置面板
    if (id) {
      set((state) => ({
        configPanel: { ...state.configPanel, isOpen: true },
      }));
    } else {
      set((state) => ({
        configPanel: { ...state.configPanel, isOpen: false },
      }));
    }
  },
  setIsDebugMode: (isDebugMode: boolean) => {
    set((state) => ({
      isDebugMode,
      debugPanel: { ...state.debugPanel, isOpen: isDebugMode },
    }));
  },
  setIsDirty: (isDirty: boolean) => {
    set({ isDirty });
  },
  setDebugPanel: (debugPanel: Partial<PanelState>) => {
    set((state) => ({
      debugPanel: {
        ...state.debugPanel,
        ...debugPanel,
        width:
          debugPanel.isOpen === false
            ? 360
            : debugPanel.width || state.debugPanel.width,
      },
    }));
  },
  setConfigPanel: (configPanel: Partial<PanelState>) => {
    set((state) => ({
      configPanel: {
        ...state.configPanel,
        ...configPanel,
        width:
          configPanel.isOpen === false
            ? 360
            : configPanel.width || state.configPanel.width,
      },
    }));
  },
  updateNode: (nodeId: string, updates: Partial<BuiltinNode>) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedNodes = currentWorkflow.nodes.map((node) =>
      node.id === nodeId ? { ...node, ...updates } : node,
    );

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });
  },
  addNode: (node: Omit<BuiltinNode, "id">) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedNodes = [...currentWorkflow.nodes, { ...node, id: uuid() }];

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });
  },
  deleteNode: (nodeId: string) => {
    const { currentWorkflow, selectedNodeId } = get();
    if (!currentWorkflow) return;

    const updatedNodes = currentWorkflow.nodes.filter(
      (node) => node.id !== nodeId,
    );

    // 删除与该节点相关的所有边
    const updatedEdges = currentWorkflow.edges.filter(
      (edge) => edge.source !== nodeId && edge.target !== nodeId,
    );

    // 如果被删除的节点当前被选中，关闭配置面板
    if (selectedNodeId === nodeId) {
      set({ selectedNodeId: null });
    }

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
        edges: updatedEdges,
      },
    });
  },
  copyNode: (nodeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const originalNode = currentWorkflow.nodes.find(
      (node) => node.id === nodeId,
    );
    if (!originalNode) return;

    // 生成唯一的节点名称
    const generateUniqueName = (baseName: string): string => {
      const existingNames = currentWorkflow.nodes.map((node) => node.label);

      // 提取基础名称（去掉可能存在的序号）
      const baseNameMatch = baseName.match(/^(.+?)(?:\s*\((\d+)\))?$/);
      const cleanBaseName = baseNameMatch ? baseNameMatch[1].trim() : baseName;

      // 查找已存在的最大序号
      let maxNumber = 0;
      existingNames.forEach((name) => {
        // 检查是否是同一个基础名称
        const match = name.match(
          new RegExp(
            `^${cleanBaseName.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\s*\\((\\d+)\\)$`,
          ),
        );
        if (match) {
          // 如果匹配到了，提取序号
          const number = parseInt(match[1], 10);
          maxNumber = Math.max(maxNumber, number);
        } else if (name === cleanBaseName) {
          // 如果是基础名称本身，视为序号0
          maxNumber = Math.max(maxNumber, 0);
        }
      });

      return `${cleanBaseName} (${maxNumber + 1})`;
    };

    // 偏移量随机，在-30到30之间
    const offset = Math.floor(Math.random() * 60) - 30;
    const label = generateUniqueName(originalNode.label);

    // 创建复制的节点
    const copiedNode: BuiltinNode = {
      ...originalNode,
      id: uuid(),
      label,
      position: {
        x: originalNode.position.x + 280 + offset,
        y: originalNode.position.y + 50 + offset,
      },
      data: {
        ...originalNode.data,
        label,
      },
    };

    const updatedNodes = [...currentWorkflow.nodes, copiedNode];

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });

    return copiedNode.id;
  },
  addEdge: (edge: Omit<BaseEdge, "id">) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedEdges = [...currentWorkflow.edges, { ...edge, id: uuid() }];

    set({
      currentWorkflow: {
        ...currentWorkflow,
        edges: updatedEdges,
      },
    });
  },
  deleteEdge: (edgeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedEdges = currentWorkflow.edges.filter(
      (edge) => edge.id !== edgeId,
    );

    set({
      currentWorkflow: {
        ...currentWorkflow,
        edges: updatedEdges,
      },
    });
  },
  getAvailableVariables: (nodeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return [];

    const node = currentWorkflow.nodes.find((node) => node.id === nodeId);
    if (!node) return [];

    const variables = [];

    return variables;
  },
}));
